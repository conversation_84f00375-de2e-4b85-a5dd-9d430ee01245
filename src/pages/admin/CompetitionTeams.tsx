"use client";
import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { ArrowLeft, Users, Trash2, Trophy, Loader2, Mail } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useAuthStore } from "@/store/authStore";
import { apiService } from "@/services/api";
import { toast } from "sonner";

interface CompetitionTeam {
  id: string;
  competitionId: string;
  teamName: string;
  player1Id: string;
  player1Username: string;
  player1Email: string;
  player2Id: string;
  player2Username: string;
  player2Email: string;
  inviteCode: string;
  gamesPlayed: number;
  points: number;
  bonusPoints: number;
  maxGames: number;
  isActive: boolean;
  isComplete: boolean;
  registeredAt: string;
  completedAt?: string;
}

export default function CompetitionTeams() {
  const navigate = useNavigate();
  const { competitionId } = useParams<{ competitionId: string }>();
  const { user, isAuthenticated } = useAuthStore();
  const [teams, setTeams] = useState<CompetitionTeam[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [competitionName, setCompetitionName] = useState("");

  // Redirect if not admin
  useEffect(() => {
    if (!isAuthenticated || !user?.isAdmin) {
      navigate("/");
    }
  }, [isAuthenticated, user, navigate]);

  // Load teams
  useEffect(() => {
    if (competitionId) {
      loadTeams();
      loadCompetitionName();
    }
  }, [competitionId]);

  if (!isAuthenticated || !user?.isAdmin) {
    return null;
  }

  const loadTeams = async () => {
    try {
      setIsLoading(true);
      const teamsData = await apiService.getCompetitionTeams(competitionId!);
      setTeams(teamsData);
    } catch (error) {
      console.error("Error loading teams:", error);
      toast.error("Failed to load teams");
    } finally {
      setIsLoading(false);
    }
  };

  const loadCompetitionName = async () => {
    try {
      const competition = await apiService.getCompetitionById(competitionId!);
      setCompetitionName(competition.name);
    } catch (error) {
      console.error("Error loading competition:", error);
    }
  };

  const handleDeleteTeam = async (teamId: string, teamName: string) => {
    if (window.confirm(`Are you sure you want to delete team "${teamName}"? This action cannot be undone.`)) {
      try {
        await apiService.deleteCompetitionTeam(teamId);
        setTeams(prev => prev.filter(team => team.id !== teamId));
        toast.success("Team deleted successfully");
      } catch (error) {
        console.error("Error deleting team:", error);
        toast.error("Failed to delete team");
      }
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString() + " " + new Date(dateString).toLocaleTimeString();
  };

  return (
    <div className="min-h-screen bg-black text-white p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => navigate(`/admin/competitions/${competitionId}`)}
            className="text-[#E1C760] hover:bg-[#E1C760]/10"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="flex items-center gap-2">
            <Users className="h-6 w-6 text-[#E1C760]" />
            <div>
              <h1 className="text-2xl font-bold text-[#E1C760]">Competition Teams</h1>
              {competitionName && (
                <p className="text-gray-400">{competitionName}</p>
              )}
            </div>
          </div>
        </div>

        {/* Loading State */}
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 text-[#E1C760] animate-spin" />
          </div>
        ) : (
          <>
            {/* Teams List */}
            <div className="space-y-4">
              {teams.map((team) => (
                <Card key={team.id} className="bg-black/50 border-[#E1C760]/30">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-[#E1C760] text-lg flex items-center gap-2">
                          <Trophy className="h-5 w-5" />
                          {team.teamName}
                        </CardTitle>
                        <CardDescription className="text-gray-400 mt-1">
                          Invite Code: {team.inviteCode}
                        </CardDescription>
                      </div>
                      <div className="flex gap-2">
                        {team.isComplete && (
                          <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
                            Complete
                          </Badge>
                        )}
                        {!team.isActive && (
                          <Badge className="bg-red-500/20 text-red-400 border-red-500/30">
                            Inactive
                          </Badge>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Players */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-black/30 rounded-lg p-3">
                        <h4 className="text-white font-semibold mb-2">Player 1</h4>
                        <p className="text-gray-300">{team.player1Username}</p>
                        <p className="text-gray-400 text-sm flex items-center gap-1">
                          <Mail className="h-3 w-3" />
                          {team.player1Email}
                        </p>
                      </div>
                      <div className="bg-black/30 rounded-lg p-3">
                        <h4 className="text-white font-semibold mb-2">Player 2</h4>
                        <p className="text-gray-300">{team.player2Username}</p>
                        <p className="text-gray-400 text-sm flex items-center gap-1">
                          <Mail className="h-3 w-3" />
                          {team.player2Email}
                        </p>
                      </div>
                    </div>

                    {/* Stats */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-400">Games Played:</span>
                        <span className="text-white ml-2">{team.gamesPlayed}/{team.maxGames}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Points:</span>
                        <span className="text-white ml-2">{team.points}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Bonus Points:</span>
                        <span className="text-white ml-2">{team.bonusPoints}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Total:</span>
                        <span className="text-white ml-2 font-semibold">{team.points + team.bonusPoints}</span>
                      </div>
                    </div>

                    {/* Dates */}
                    <div className="text-sm text-gray-500">
                      <p>Registered: {formatDate(team.registeredAt)}</p>
                      {team.completedAt && (
                        <p>Completed: {formatDate(team.completedAt)}</p>
                      )}
                    </div>

                    {/* Actions */}
                    <div className="flex justify-end">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteTeam(team.id, team.teamName)}
                        className="border-red-500/30 text-red-400 hover:bg-red-500/10"
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        Delete Team
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Empty State */}
            {teams.length === 0 && (
              <div className="text-center py-12">
                <Users className="h-16 w-16 text-gray-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-400 mb-2">No Teams Found</h3>
                <p className="text-gray-500">No teams have registered for this competition yet.</p>
              </div>
            )}

            {/* Summary */}
            {teams.length > 0 && (
              <Card className="bg-black/50 border-[#E1C760]/30 mt-8">
                <CardHeader>
                  <CardTitle className="text-[#E1C760]">Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                    <div>
                      <div className="text-2xl font-bold text-[#E1C760]">{teams.length}</div>
                      <div className="text-gray-400">Total Teams</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-green-400">{teams.filter(t => t.isActive).length}</div>
                      <div className="text-gray-400">Active Teams</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-blue-400">{teams.filter(t => t.isComplete).length}</div>
                      <div className="text-gray-400">Completed</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-yellow-400">{teams.reduce((sum, t) => sum + t.gamesPlayed, 0)}</div>
                      <div className="text-gray-400">Total Games</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </>
        )}
      </div>
    </div>
  );
}
