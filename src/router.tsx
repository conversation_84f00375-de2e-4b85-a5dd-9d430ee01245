
import { createBrowserRouter, Navigate, Outlet } from "react-router-dom";
import App from "./App";
import Lobby from "./pages/Lobby";
import GameLobby from "./pages/GameLobby";
import SpectateGames from "./pages/SpectateGames";
import Competitions from "./pages/Competitions";
import CompetitionLeaderboard from "./pages/CompetitionLeaderboard";
import CompetitionLobby from "./pages/CompetitionLobby";
import GlobalLeaderboard from "./pages/GlobalLeaderboard";
import RulesTest from "./pages/RulesTest";
import DealerTest from "./pages/DealerTest";
import TrumpDisplayTest from "./pages/TrumpDisplayTest";
import TimeSettings from "./pages/TimeSettings";
import CardImageSettings from "./pages/CardImageSettings";
import Login from "./pages/Login";
import Register from "./pages/Register";
import AdminDashboard from "./pages/AdminDashboard";
import AdminTimeSettings from "./pages/admin/TimeSettings";
import AdminCardImageSettings from "./pages/admin/CardImageSettings";
import CompetitionManagement from "./pages/admin/CompetitionManagement";
import UserManagement from "./pages/admin/UserManagement";
import CreateCompetition from "./pages/admin/CreateCompetition";
import CompetitionDetails from "./pages/admin/CompetitionDetails";
import CompetitionTeams from "./pages/admin/CompetitionTeams";
import EditCompetition from "./pages/admin/EditCompetition";
import CompetitionEmail from "./pages/admin/CompetitionEmail";
import { useAuthStore } from "./store/authStore";

// Protected route wrapper
const ProtectedRoute = () => {
  const { isAuthenticated } = useAuthStore();

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return <Outlet />;
};

// Admin route wrapper
const AdminRoute = () => {
  const { isAuthenticated, user } = useAuthStore();

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (!user?.isAdmin) {
    return <Navigate to="/" replace />;
  }

  return <Outlet />;
};

const router = createBrowserRouter([
  // Public routes
  {
    path: "/login",
    element: <Login />,
  },
  {
    path: "/register",
    element: <Register />,
  },

  // Protected routes
  {
    element: <ProtectedRoute />,
    children: [
      {
        path: "/",
        element: <Lobby />,
      },
      {
        path: "/lobby",
        element: <GameLobby />,
      },
      {
        path: "/game",
        element: <App />,
      },
      {
        path: "/spectate",
        element: <SpectateGames />,
      },
      {
        path: "/tutorial",
        element: <div className="h-screen flex items-center justify-center text-white">Tutorial Page (Coming Soon)</div>,
      },
      {
        path: "/settings",
        element: <TimeSettings />,
      },
      {
        path: "/card-settings",
        element: <CardImageSettings />,
      },
      {
        path: "/competitions",
        element: <Competitions />,
      },
      {
        path: "/competitions/:id/lobby",
        element: <CompetitionLobby />,
      },
      {
        path: "/competitions/:id",
        element: <CompetitionLeaderboard />,
      },
      {
        path: "/leaderboard",
        element: <GlobalLeaderboard />,
      },
    ],
  },

  // Admin routes
  {
    element: <AdminRoute />,
    children: [
      {
        path: "/admin",
        element: <AdminDashboard />,
      },
      {
        path: "/admin/time-settings",
        element: <AdminTimeSettings />,
      },
      {
        path: "/admin/card-settings",
        element: <AdminCardImageSettings />,
      },
      {
        path: "/admin/competitions",
        element: <CompetitionManagement />,
      },
      {
        path: "/admin/users",
        element: <UserManagement />,
      },
      {
        path: "/admin/competitions/create",
        element: <CreateCompetition />,
      },
      {
        path: "/admin/competitions/:competitionId",
        element: <CompetitionDetails />,
      },
      {
        path: "/admin/competitions/:competitionId/teams",
        element: <CompetitionTeams />,
      },
      {
        path: "/admin/competitions/:competitionId/edit",
        element: <EditCompetition />,
      },
      {
        path: "/admin/competitions/:competitionId/email",
        element: <CompetitionEmail />,
      },
    ],
  },

  // Test routes (can be protected later if needed)
  {
    path: "/rules-test",
    element: <RulesTest />,
  },
  {
    path: "/dealer-test",
    element: <DealerTest />,
  },
  {
    path: "/trump-test",
    element: <TrumpDisplayTest />,
  },
]);

export default router;
