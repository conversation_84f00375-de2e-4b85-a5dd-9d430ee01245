using ThuneeAPI.Core.Entities;

namespace ThuneeAPI.Application.Interfaces;

/// <summary>
/// Repository interface for Game entity operations
/// </summary>
public interface IGameRepository
{
    /// <summary>
    /// Creates a new game
    /// </summary>
    /// <param name="game">Game entity to create</param>
    /// <returns>Created game with generated ID</returns>
    Task<Game> CreateAsync(Game game);

    /// <summary>
    /// Gets a game by its ID
    /// </summary>
    /// <param name="id">Game ID</param>
    /// <returns>Game entity or null if not found</returns>
    Task<Game?> GetByIdAsync(Guid id);

    /// <summary>
    /// Gets a game by its lobby code
    /// </summary>
    /// <param name="lobbyCode">Lobby code to search for</param>
    /// <returns>Game entity or null if not found</returns>
    Task<Game?> GetByLobbyCodeAsync(string lobbyCode);

    /// <summary>
    /// Updates a game entity
    /// </summary>
    /// <param name="game">Game entity to update</param>
    /// <returns>Task representing the operation</returns>
    Task UpdateAsync(Game game);

    /// <summary>
    /// Joins a player to a game
    /// </summary>
    /// <param name="lobbyCode">Lobby code of the game</param>
    /// <param name="playerId">Player ID to join</param>
    /// <returns>Updated game entity</returns>
    Task<Game?> JoinGameAsync(string lobbyCode, Guid playerId);

    /// <summary>
    /// Records a hand result for a game
    /// </summary>
    /// <param name="gameId">Game ID</param>
    /// <param name="ballNumber">Ball number</param>
    /// <param name="handNumber">Hand number</param>
    /// <param name="winnerPlayerId">Winner player ID</param>
    /// <param name="points">Points scored</param>
    /// <returns>Hand ID</returns>
    Task<Guid> RecordHandResultAsync(Guid gameId, int ballNumber, int handNumber, Guid winnerPlayerId, int points);

    /// <summary>
    /// Checks if a lobby code exists
    /// </summary>
    /// <param name="lobbyCode">Lobby code to check</param>
    /// <returns>True if lobby code exists, false otherwise</returns>
    Task<bool> LobbyCodeExistsAsync(string lobbyCode);

    /// <summary>
    /// Gets games for a specific competition
    /// </summary>
    /// <param name="competitionId">Competition ID</param>
    /// <returns>Collection of games</returns>
    Task<IEnumerable<Game>> GetByCompetitionIdAsync(Guid competitionId);

    /// <summary>
    /// Gets games for a specific player
    /// </summary>
    /// <param name="playerId">Player ID</param>
    /// <returns>Collection of games</returns>
    Task<IEnumerable<Game>> GetByPlayerIdAsync(Guid playerId);

    /// <summary>
    /// Gets all games
    /// </summary>
    /// <returns>Collection of all games</returns>
    Task<IEnumerable<Game>> GetAllAsync();

    /// <summary>
    /// Gets active games (waiting or in-progress)
    /// </summary>
    /// <returns>Collection of active games</returns>
    Task<IEnumerable<Game>> GetActiveGamesAsync();

    /// <summary>
    /// Deletes a game
    /// </summary>
    /// <param name="id">Game ID</param>
    /// <returns>Task representing the operation</returns>
    Task DeleteAsync(Guid id);
}
